2025-08-30 15:15:30.931 [com.ray.demo.netty.NettyServer.main()] INFO  com.ray.demo.netty.NettyServer - Netty配置文件加载成功
2025-08-30 15:15:31.013 [com.ray.demo.netty.NettyServer.main()] INFO  com.ray.demo.netty.NettyServer - Netty服务器启动中...
2025-08-30 15:15:31.013 [com.ray.demo.netty.NettyServer.main()] INFO  com.ray.demo.netty.NettyServer - 服务器地址: localhost:8080
2025-08-30 15:15:31.014 [com.ray.demo.netty.NettyServer.main()] INFO  com.ray.demo.netty.NettyServer - Boss线程数: 1, Worker线程数: 4
2025-08-30 15:15:31.538 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xdc3179a7] REGISTERED
2025-08-30 15:15:31.541 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xdc3179a7] BIND: localhost/127.0.0.1:8080
2025-08-30 15:15:31.542 [com.ray.demo.netty.NettyServer.main()] INFO  com.ray.demo.netty.NettyServer - Netty服务器启动成功，监听端口: 8080
2025-08-30 15:15:31.543 [com.ray.demo.netty.NettyServer.main()] INFO  com.ray.demo.netty.NettyServer - 服务器特性:
2025-08-30 15:15:31.543 [com.ray.demo.netty.NettyServer.main()] INFO  com.ray.demo.netty.NettyServer -   - 基于Netty的高性能异步网络框架
2025-08-30 15:15:31.543 [com.ray.demo.netty.NettyServer.main()] INFO  com.ray.demo.netty.NettyServer -   - 支持高并发连接和消息处理
2025-08-30 15:15:31.543 [com.ray.demo.netty.NettyServer.main()] INFO  com.ray.demo.netty.NettyServer -   - 内置心跳检测和连接管理
2025-08-30 15:15:31.543 [com.ray.demo.netty.NettyServer.main()] INFO  com.ray.demo.netty.NettyServer -   - 优雅的事件驱动架构
2025-08-30 15:15:31.544 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xdc3179a7, L:/127.0.0.1:8080] ACTIVE
2025-08-30 15:15:45.464 [com.ray.demo.netty.NettyClient.main()] INFO  com.ray.demo.netty.NettyClient - 正在连接到Netty服务器 localhost:8080...
2025-08-30 15:15:45.613 [nioEventLoopGroup-2-1] INFO  com.ray.demo.netty.NettyClientHandler - 与服务器建立连接: localhost/127.0.0.1:8080
2025-08-30 15:15:45.623 [com.ray.demo.netty.NettyClient.main()] INFO  com.ray.demo.netty.NettyClient - 成功连接到Netty服务器！连接ID: 2d87cde6
2025-08-30 15:15:45.634 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xdc3179a7, L:/127.0.0.1:8080] READ: [id: 0x14b7e98a, L:/127.0.0.1:8080 - R:/127.0.0.1:55740]
2025-08-30 15:15:45.636 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xdc3179a7, L:/127.0.0.1:8080] READ COMPLETE
2025-08-30 15:15:45.658 [nioEventLoopGroup-3-1] INFO  com.ray.demo.netty.NettyServerHandler - 新客户端连接: 127.0.0.1:55740 (连接ID: 14b7e98a, 当前连接数: 1)
2025-08-30 15:16:10.384 [Thread-2] INFO  com.ray.demo.netty.NettyServer - 收到关闭信号，正在优雅关闭服务器...
2025-08-30 15:16:10.384 [Thread-2] INFO  com.ray.demo.netty.NettyServer - 正在关闭Netty服务器...
2025-08-30 15:16:10.385 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xdc3179a7, L:/127.0.0.1:8080] CLOSE
2025-08-30 15:16:10.386 [Thread-2] INFO  com.ray.demo.netty.NettyServer - Netty服务器已关闭
2025-08-30 15:16:10.388 [com.ray.demo.netty.NettyClient.main()] INFO  com.ray.demo.netty.NettyClient - 正在断开与Netty服务器的连接...
2025-08-30 15:16:10.388 [nioEventLoopGroup-2-1] INFO  com.ray.demo.netty.NettyClientHandler - 与服务器断开连接: localhost/127.0.0.1:8080
2025-08-30 15:16:10.392 [com.ray.demo.netty.NettyClient.main()] INFO  com.ray.demo.netty.NettyClient - 已断开与Netty服务器的连接
2025-08-30 15:16:15.487 [Thread-2] INFO  com.ray.demo.netty.NettyClient - 收到关闭信号，正在断开连接...
2025-08-30 15:16:15.487 [Thread-2] INFO  com.ray.demo.netty.NettyClient - 正在断开与Netty服务器的连接...
2025-08-30 15:16:15.487 [Thread-2] INFO  com.ray.demo.netty.NettyClient - 已断开与Netty服务器的连接
2025-08-30 15:16:25.326 [com.ray.demo.netty.NettyHttpServer.main()] INFO  com.ray.demo.netty.NettyHttpServer - Netty HTTP服务器启动中...
2025-08-30 15:16:25.329 [com.ray.demo.netty.NettyHttpServer.main()] INFO  com.ray.demo.netty.NettyHttpServer - 服务器地址: http://localhost:8081
2025-08-30 15:16:25.435 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xe53fefd9] REGISTERED
2025-08-30 15:16:25.438 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xe53fefd9] BIND: 0.0.0.0/0.0.0.0:8081
2025-08-30 15:16:25.440 [com.ray.demo.netty.NettyHttpServer.main()] INFO  com.ray.demo.netty.NettyHttpServer - Netty HTTP服务器启动成功！
2025-08-30 15:16:25.440 [com.ray.demo.netty.NettyHttpServer.main()] INFO  com.ray.demo.netty.NettyHttpServer - 支持的端点:
2025-08-30 15:16:25.440 [com.ray.demo.netty.NettyHttpServer.main()] INFO  com.ray.demo.netty.NettyHttpServer -   GET  /           - 首页
2025-08-30 15:16:25.440 [com.ray.demo.netty.NettyHttpServer.main()] INFO  com.ray.demo.netty.NettyHttpServer -   GET  /api/info   - 服务器信息API
2025-08-30 15:16:25.440 [com.ray.demo.netty.NettyHttpServer.main()] INFO  com.ray.demo.netty.NettyHttpServer -   GET  /api/time   - 当前时间API
2025-08-30 15:16:25.441 [com.ray.demo.netty.NettyHttpServer.main()] INFO  com.ray.demo.netty.NettyHttpServer -   POST /api/echo   - 回显API
2025-08-30 15:16:25.441 [com.ray.demo.netty.NettyHttpServer.main()] INFO  com.ray.demo.netty.NettyHttpServer -   GET  /health     - 健康检查
2025-08-30 15:16:25.441 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xe53fefd9, L:/0:0:0:0:0:0:0:0:8081] ACTIVE
2025-08-30 15:16:37.611 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xe53fefd9, L:/0:0:0:0:0:0:0:0:8081] READ: [id: 0xf0ed4878, L:/127.0.0.1:8081 - R:/127.0.0.1:55831]
2025-08-30 15:16:37.612 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xe53fefd9, L:/0:0:0:0:0:0:0:0:8081] READ COMPLETE
2025-08-30 15:16:37.691 [nioEventLoopGroup-3-1] INFO  c.r.d.netty.NettyHttpServer$NettyHttpServerHandler - 收到HTTP请求: GET /api/info from /127.0.0.1:55831
2025-08-30 15:16:44.596 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xe53fefd9, L:/0:0:0:0:0:0:0:0:8081] READ: [id: 0x38d63377, L:/127.0.0.1:8081 - R:/127.0.0.1:55844]
2025-08-30 15:16:44.597 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xe53fefd9, L:/0:0:0:0:0:0:0:0:8081] READ COMPLETE
2025-08-30 15:16:44.599 [nioEventLoopGroup-3-2] INFO  c.r.d.netty.NettyHttpServer$NettyHttpServerHandler - 收到HTTP请求: GET /api/time from /127.0.0.1:55844
2025-08-30 15:16:52.581 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xe53fefd9, L:/0:0:0:0:0:0:0:0:8081] READ: [id: 0x3d00d37c, L:/127.0.0.1:8081 - R:/127.0.0.1:55866]
2025-08-30 15:16:52.582 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xe53fefd9, L:/0:0:0:0:0:0:0:0:8081] READ COMPLETE
2025-08-30 15:16:52.586 [nioEventLoopGroup-3-3] INFO  c.r.d.netty.NettyHttpServer$NettyHttpServerHandler - 收到HTTP请求: GET /health from /127.0.0.1:55866
2025-08-30 15:16:59.826 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xe53fefd9, L:/0:0:0:0:0:0:0:0:8081] READ: [id: 0xadcfd3b4, L:/127.0.0.1:8081 - R:/127.0.0.1:55913]
2025-08-30 15:16:59.826 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xe53fefd9, L:/0:0:0:0:0:0:0:0:8081] READ COMPLETE
2025-08-30 15:16:59.832 [nioEventLoopGroup-3-4] INFO  c.r.d.netty.NettyHttpServer$NettyHttpServerHandler - 收到HTTP请求: POST /api/echo from /127.0.0.1:55913
2025-08-30 15:17:14.090 [Thread-2] INFO  com.ray.demo.netty.NettyHttpServer - 收到关闭信号，正在关闭HTTP服务器...
2025-08-30 15:17:14.090 [Thread-2] INFO  com.ray.demo.netty.NettyHttpServer - 正在关闭Netty HTTP服务器...
2025-08-30 15:17:14.091 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xe53fefd9, L:/0:0:0:0:0:0:0:0:8081] CLOSE
2025-08-30 15:17:14.092 [com.ray.demo.netty.NettyHttpServer.main()] INFO  com.ray.demo.netty.NettyHttpServer - 正在关闭Netty HTTP服务器...
2025-08-30 15:17:14.092 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xe53fefd9, L:/0:0:0:0:0:0:0:0:8081] INACTIVE
2025-08-30 15:17:14.092 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0xe53fefd9, L:/0:0:0:0:0:0:0:0:8081] UNREGISTERED
2025-08-30 15:17:14.097 [Thread-2] INFO  com.ray.demo.netty.NettyHttpServer - Netty HTTP服务器已关闭
2025-08-30 15:17:14.097 [com.ray.demo.netty.NettyHttpServer.main()] INFO  com.ray.demo.netty.NettyHttpServer - Netty HTTP服务器已关闭
2025-08-30 15:17:23.510 [com.ray.demo.netty.NettyWebSocketServer.main()] INFO  com.ray.demo.netty.NettyWebSocketServer - Netty WebSocket服务器启动中...
2025-08-30 15:17:23.511 [com.ray.demo.netty.NettyWebSocketServer.main()] INFO  com.ray.demo.netty.NettyWebSocketServer - 服务器地址: ws://localhost:8082/websocket
2025-08-30 15:17:23.601 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0x435eec42] REGISTERED
2025-08-30 15:17:23.603 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0x435eec42] BIND: 0.0.0.0/0.0.0.0:8082
2025-08-30 15:17:23.604 [com.ray.demo.netty.NettyWebSocketServer.main()] INFO  com.ray.demo.netty.NettyWebSocketServer - Netty WebSocket服务器启动成功！
2025-08-30 15:17:23.605 [com.ray.demo.netty.NettyWebSocketServer.main()] INFO  com.ray.demo.netty.NettyWebSocketServer - WebSocket端点: ws://localhost:8082/websocket
2025-08-30 15:17:23.605 [com.ray.demo.netty.NettyWebSocketServer.main()] INFO  com.ray.demo.netty.NettyWebSocketServer - 测试页面: http://localhost:8082/
2025-08-30 15:17:23.606 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0x435eec42, L:/0:0:0:0:0:0:0:0:8082] ACTIVE
2025-08-30 15:17:34.136 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0x435eec42, L:/0:0:0:0:0:0:0:0:8082] READ: [id: 0xfd68b97d, L:/127.0.0.1:8082 - R:/127.0.0.1:55986]
2025-08-30 15:17:34.137 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0x435eec42, L:/0:0:0:0:0:0:0:0:8082] READ COMPLETE
2025-08-30 15:17:34.220 [nioEventLoopGroup-3-1] INFO  c.r.d.n.NettyWebSocketServer$WebSocketServerHandler - WebSocket连接断开: fd68b97d (剩余连接数: -1)
2025-08-30 15:17:40.716 [Thread-2] INFO  com.ray.demo.netty.NettyWebSocketServer - 收到关闭信号，正在关闭WebSocket服务器...
2025-08-30 15:17:40.716 [Thread-2] INFO  com.ray.demo.netty.NettyWebSocketServer - 正在关闭Netty WebSocket服务器...
2025-08-30 15:17:40.718 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0x435eec42, L:/0:0:0:0:0:0:0:0:8082] CLOSE
2025-08-30 15:17:40.719 [com.ray.demo.netty.NettyWebSocketServer.main()] INFO  com.ray.demo.netty.NettyWebSocketServer - 正在关闭Netty WebSocket服务器...
2025-08-30 15:17:40.719 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0x435eec42, L:/0:0:0:0:0:0:0:0:8082] INACTIVE
2025-08-30 15:17:40.719 [nioEventLoopGroup-2-1] INFO  io.netty.handler.logging.LoggingHandler - [id: 0x435eec42, L:/0:0:0:0:0:0:0:0:8082] UNREGISTERED
2025-08-30 15:17:40.724 [Thread-2] INFO  com.ray.demo.netty.NettyWebSocketServer - Netty WebSocket服务器已关闭
2025-08-30 15:17:40.724 [com.ray.demo.netty.NettyWebSocketServer.main()] INFO  com.ray.demo.netty.NettyWebSocketServer - Netty WebSocket服务器已关闭
